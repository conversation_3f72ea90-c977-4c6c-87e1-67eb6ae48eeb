# Borouge ESG Intelligence Platform

A sophisticated ESG (Environmental, Social, Governance) intelligence platform built for Borouge, providing executive-ready business intelligence reports with priority-based article systems.

## Features

- **Executive Intelligence Reports**: Priority-based article system with critical regulatory compliance and high financial impact analysis
- **ESG Analytics**: Comprehensive environmental, social, and governance intelligence
- **Interactive UI**: Modern React-based interface with smooth animations and responsive design
- **Search & Discovery**: Intelligent search with suggestion chips for quick access to relevant ESG topics
- **Professional Presentation**: Executive-ready formatting suitable for C-level decision making

## Getting Started

This project was bootstrapped with [Create React App](https://github.com/facebook/create-react-app).

### Prerequisites

- Node.js (v14 or higher)
- npm or yarn

### Installation

1. Clone the repository
```bash
git clone https://github.com/Mitty530/Borouge.git
cd Borouge/borouge-esg-frontend
```

2. Install dependencies
```bash
npm install
```

3. Start the development server
```bash
npm start
```

Open [http://localhost:3000](http://localhost:3000) to view it in your browser.

## Available Scripts

### `npm start`

Runs the app in the development mode.\
The page will reload when you make changes.\
You may also see any lint errors in the console.

### `npm test`

Launches the test runner in the interactive watch mode.

### `npm run build`

Builds the app for production to the `build` folder.\
It correctly bundles React in production mode and optimizes the build for the best performance.

## Architecture

- **Frontend**: React with modern hooks and functional components
- **Styling**: CSS modules with responsive design
- **Animations**: Framer Motion for smooth transitions
- **Icons**: Lucide React for consistent iconography
- **State Management**: React hooks (useState, useEffect)

## Key Components

- **ConversationView**: Main intelligence interface with article-based responses
- **Article System**: Priority-based display (Critical Regulatory Compliance → High Financial Impact)
- **Search Interface**: Intelligent query processing with suggestion chips
- **Responsive Design**: Optimized for desktop and mobile devices
